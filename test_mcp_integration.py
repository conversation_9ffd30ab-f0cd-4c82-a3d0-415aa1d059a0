#!/usr/bin/env python3
"""
Test script for MCP integration

This script demonstrates the MCP client integration with backend APIs
without requiring AWS credentials.
"""

import asyncio
import json
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.abspath('.'))
sys.path.insert(0, os.path.abspath('./app'))

from app.ai_gateway.mcp_manager import MCPManager
from app.ai_gateway.mcp_config import MCPConfigManager


async def test_mcp_config():
    """Test MCP configuration management."""
    print("🔧 Testing MCP Configuration Management...")
    
    config_manager = MCPConfigManager(".stitch/mcp.json")
    
    # Load existing config
    success = config_manager.load_config()
    print(f"✅ Config loaded: {success}")
    
    # Get all servers
    servers = config_manager.get_all_servers()
    print(f"📊 Found {len(servers)} configured servers:")
    
    for name, server in servers.items():
        print(f"  - {name}: {server.type} at {server.url}")
    
    return config_manager


async def test_mcp_manager():
    """Test MCP manager functionality."""
    print("\n🚀 Testing MCP Manager...")
    
    manager = MCPManager(".stitch/mcp.json")
    
    # Initialize manager
    success = await manager.initialize()
    print(f"✅ Manager initialized: {success}")
    
    # List available tools
    tools = manager.tools
    print(f"🔧 Found {len(tools)} available tools:")
    
    for tool_key, tool in tools.items():
        print(f"  - {tool_key}: {tool.description}")
    
    return manager


async def test_server_communication():
    """Test communication with MCP servers."""
    print("\n📡 Testing Server Communication...")
    
    manager = MCPManager(".stitch/mcp.json")
    await manager.initialize()
    
    # Check server status
    for server_name, status in manager.server_status.items():
        print(f"  - {server_name}: {status}")
    
    # Try to call a simple tool if available
    if manager.tools:
        tool_key = list(manager.tools.keys())[0]
        print(f"\n🔧 Testing tool call: {tool_key}")
        
        try:
            result = await manager.call_tool(tool_key, {})
            print(f"✅ Tool result: {result[:100]}..." if result and len(result) > 100 else f"✅ Tool result: {result}")
        except Exception as e:
            print(f"❌ Tool call failed: {e}")
    
    return manager


async def test_backend_integration():
    """Test backend integration components."""
    print("\n🔗 Testing Backend Integration...")
    
    try:
        from src.backend.app.bedrock.config import BedrockMCPConfigManager
        from src.backend.app.bedrock.bedrock_service import BedrockMCPOrchestrator
        
        # Test config manager
        config_manager = BedrockMCPConfigManager()
        success = config_manager.load_config()
        print(f"✅ Bedrock config manager: {success}")
        
        servers = config_manager.get_all_servers()
        print(f"📊 Bedrock servers: {len(servers)}")
        
        # Test orchestrator (without AWS credentials)
        print("🤖 Testing Bedrock orchestrator initialization...")
        orchestrator = BedrockMCPOrchestrator()
        
        # We can't fully initialize without AWS credentials, but we can test the structure
        print("✅ Bedrock orchestrator created successfully")
        
    except Exception as e:
        print(f"❌ Backend integration test failed: {e}")


async def demonstrate_configuration():
    """Demonstrate adding and managing MCP servers."""
    print("\n⚙️  Demonstrating Configuration Management...")
    
    config_manager = MCPConfigManager(".stitch/mcp.json")
    config_manager.load_config()
    
    # Show current configuration
    print("Current configuration:")
    servers = config_manager.get_all_servers()
    for name, server in servers.items():
        print(f"  - {name}: {server.type} at {server.url}")
    
    # Demonstrate adding a new server (example)
    print("\n📝 Example: Adding a new MCP server")
    print("POST /api/mcp/servers")
    print(json.dumps({
        "name": "example_server",
        "url": "http://localhost:8001",
        "type": "streamable-http",
        "description": "Example MCP server"
    }, indent=2))
    
    print("\n📝 Example: Natural language query")
    print("POST /api/mcp/query")
    print(json.dumps({
        "message": "What tools are available to help me analyze data?",
        "conversation_history": []
    }, indent=2))


async def main():
    """Main test function."""
    print("🧪 MCP Integration Test Suite")
    print("=" * 50)
    
    try:
        # Test configuration
        await test_mcp_config()
        
        # Test manager
        await test_mcp_manager()
        
        # Test server communication
        await test_server_communication()
        
        # Test backend integration
        await test_backend_integration()
        
        # Demonstrate configuration
        await demonstrate_configuration()
        
        print("\n" + "=" * 50)
        print("✅ All tests completed!")
        print("\n🌐 Web Interface: http://localhost:8002/mcp")
        print("📚 API Documentation: http://localhost:8002/docs")
        print("\n🚀 Ready for natural language queries through MCP tools!")
        
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
