# MCP Client Integration Guide

## Overview

This implementation provides a complete MCP (Model Context Protocol) client integration with backend APIs that enables natural language queries to be routed through LLM services and executed via MCP tools. The system is configurable to support any HTTP server type, including streamable-http servers.

## Architecture

### Components

1. **MCP Configuration System** (`app/ai_gateway/mcp_config.py`)
   - Manages MCP server configurations via JSON files
   - Supports multiple server types: `http`, `streamable-http`, `subprocess`
   - Configurable through `.stitch/mcp.json`

2. **MCP Manager** (`app/ai_gateway/mcp_manager.py`)
   - Handles connections to multiple MCP servers
   - Discovers and manages available tools
   - Routes tool calls to appropriate servers

3. **Bedrock MCP Orchestrator** (`src/backend/app/bedrock/bedrock_service.py`)
   - Processes natural language queries using AWS Bedrock LLM
   - Determines which MCP tools to use based on user intent
   - Streams responses with tool execution indicators

4. **Backend API Endpoints** (`src/backend/app/main.py`)
   - RESTful APIs for natural language queries
   - WebSocket support for real-time streaming
   - Configuration management endpoints

5. **Frontend Interface** (`src/backend/static/mcp_chat.html`)
   - Natural language chat interface
   - Real-time streaming with tool execution feedback
   - Server configuration management

## Key Features

### ✅ Natural Language Interface
- Users can ask questions in plain English
- LLM determines which tools to use automatically
- Supports conversation history and context

### ✅ Configurable MCP Servers
- Add any HTTP server type through configuration
- Support for streamable-http servers (e.g., localhost:8001)
- Dynamic server addition via API

### ✅ Real-time Streaming
- WebSocket connections for live responses
- Tool execution indicators
- Progressive response building

### ✅ Tool Discovery and Execution
- Automatic discovery of available tools from MCP servers
- Intelligent tool selection based on user queries
- Error handling and fallback mechanisms

## API Endpoints

### Natural Language Query
```http
POST /api/mcp/query
Content-Type: application/json

{
  "message": "What tools are available to help me analyze data?",
  "conversation_history": []
}
```

### Server Management
```http
# Get all configured servers
GET /api/mcp/servers

# Add a new MCP server
POST /api/mcp/servers
Content-Type: application/json

{
  "name": "my_server",
  "url": "http://localhost:8001",
  "type": "streamable-http",
  "description": "My custom MCP server"
}
```

### WebSocket Streaming
```javascript
const ws = new WebSocket('ws://localhost:8002/ws/mcp/stream');
ws.send(JSON.stringify({
  "type": "query",
  "message": "Help me analyze sales data"
}));
```

## Configuration

### MCP Server Configuration (`.stitch/mcp.json`)
```json
{
  "mcpServers": {
    "my_server": {
      "type": "streamable-http",
      "url": "http://localhost:8001",
      "disabled": false,
      "description": "Custom MCP server",
      "alwaysAllow": []
    },
    "filesystem": {
      "type": "subprocess",
      "command": ["npx", "-y", "@modelcontextprotocol/server-filesystem"],
      "args": ["/tmp"],
      "disabled": false
    }
  }
}
```

### Environment Variables
```bash
# AWS Bedrock Configuration
BEDROCK_MODEL_ID=anthropic.claude-3-5-sonnet-20240620-v1:0
AWS_REGION=ap-southeast-1
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
```

## Usage Examples

### 1. Adding a New MCP Server
```bash
curl -X POST http://localhost:8002/api/mcp/servers \
  -H "Content-Type: application/json" \
  -d '{
    "name": "data_analysis",
    "url": "http://localhost:8001",
    "type": "streamable-http",
    "description": "Data analysis MCP server"
  }'
```

### 2. Natural Language Query
```bash
curl -X POST http://localhost:8002/api/mcp/query \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Show me the available databases and help me query sales data",
    "conversation_history": []
  }'
```

### 3. Using the Web Interface
1. Open http://localhost:8002/mcp
2. Add MCP servers using the configuration form
3. Ask natural language questions
4. See real-time tool execution and responses

## Testing

### Run the Test Suite
```bash
python test_mcp_integration.py
```

### Start the Backend Server
```bash
cd src/backend
python -c "
import uvicorn
from app.main import app
uvicorn.run(app, host='0.0.0.0', port=8002)
"
```

### Test API Endpoints
```bash
# Health check
curl http://localhost:8002/

# List servers
curl http://localhost:8002/api/mcp/servers

# Natural language query
curl -X POST http://localhost:8002/api/mcp/query \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello, what can you help me with?"}'
```

## Implementation Details

### MCP Client Architecture
The implementation follows the MCP specification for building clients:

1. **Server Connection Management**
   - Supports multiple transport types (HTTP, subprocess)
   - Automatic reconnection and error handling
   - Health monitoring and status tracking

2. **Tool Discovery**
   - Dynamic tool discovery from connected servers
   - Tool metadata caching and management
   - Parameter validation and schema handling

3. **Query Processing**
   - Natural language understanding via LLM
   - Intelligent tool selection and orchestration
   - Response streaming and real-time updates

### Error Handling
- Graceful degradation when servers are unavailable
- Comprehensive error messages and logging
- Fallback mechanisms for failed tool calls

### Security Considerations
- Input validation and sanitization
- Secure credential management
- Rate limiting and access controls

## Next Steps

1. **Enhanced Tool Selection**: Implement more sophisticated tool selection algorithms
2. **Caching**: Add response caching for frequently used tools
3. **Monitoring**: Implement comprehensive monitoring and analytics
4. **Authentication**: Add user authentication and authorization
5. **Plugin System**: Create a plugin architecture for custom tool integrations

## Troubleshooting

### Common Issues

1. **Server Connection Errors**
   - Check if MCP servers are running
   - Verify URLs and ports in configuration
   - Check network connectivity

2. **AWS Credentials**
   - Ensure AWS credentials are properly configured
   - Check IAM permissions for Bedrock access
   - Verify region settings

3. **Tool Discovery Issues**
   - Check server logs for errors
   - Verify MCP server compatibility
   - Test server endpoints manually

### Logs and Debugging
- Backend logs: Check console output when running the server
- MCP Manager logs: Enable debug logging in `mcp_manager.py`
- Frontend logs: Check browser console for JavaScript errors

## Resources

- [Model Context Protocol Documentation](https://modelcontextprotocol.io/)
- [MCP Client Building Guide](https://modelcontextprotocol.io/docs/develop/build-client)
- [AWS Bedrock Documentation](https://docs.aws.amazon.com/bedrock/)
- [FastAPI Documentation](https://fastapi.tiangolo.com/)
