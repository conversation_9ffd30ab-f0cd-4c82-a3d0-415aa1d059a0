"""
Bedrock MCP Orchestrator

Orchestrates natural language queries through AWS Bedrock LLM and MCP tools.
Handles tool selection, execution, and response streaming.
"""

import asyncio
import json
import logging
import os
from typing import Dict, List, Optional, Any, AsyncIterator
from dataclasses import dataclass
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# AWS Bedrock imports
try:
    from langchain_aws import ChatBedrock
    from langchain_core.messages import HumanMessage, SystemMessage, AIMessage
    BEDROCK_AVAILABLE = True
except ImportError:
    BEDROCK_AVAILABLE = False
    print("AWS Bedrock not available - using fallback mode")

# Import MCP components
import sys
import os
# Add the project root to the path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
sys.path.insert(0, project_root)

try:
    from app.ai_gateway.mcp_manager import <PERSON>PManager, MCPTool
except ImportError:
    # Fallback for development
    sys.path.insert(0, os.path.join(project_root, 'app'))
    from ai_gateway.mcp_manager import <PERSON><PERSON>ana<PERSON>, MCPTool

from .config import BedrockMCPConfigManager

logger = logging.getLogger(__name__)


@dataclass
class ToolCall:
    """Represents a tool call made during query processing."""
    tool_name: str
    arguments: Dict[str, Any]
    result: Optional[str] = None
    error: Optional[str] = None


@dataclass
class QueryResult:
    """Result of processing a natural language query."""
    answer: str
    tool_calls: List[ToolCall]
    intermediate_steps: List[str]
    conversation_history: List[Dict[str, str]]


class BedrockMCPOrchestrator:
    """Orchestrates natural language queries through Bedrock and MCP tools."""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_manager = BedrockMCPConfigManager(config_path)
        self.mcp_manager = MCPManager(config_path)
        self.llm = None
        self.available_tools: Dict[str, MCPTool] = {}
        self.conversation_history: List[Dict[str, str]] = []
        
    async def initialize(self) -> bool:
        """Initialize the orchestrator."""
        try:
            # Initialize LLM
            self._initialize_llm()
            
            # Load configuration
            if not self.config_manager.load_config():
                logger.error("Failed to load Bedrock MCP configuration")
                return False
            
            # Initialize MCP manager
            if not await self.mcp_manager.initialize():
                logger.error("Failed to initialize MCP manager")
                return False
            
            # Load available tools
            await self._load_available_tools()
            
            logger.info(f"Bedrock MCP Orchestrator initialized with {len(self.available_tools)} tools")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Bedrock MCP Orchestrator: {e}")
            return False
    
    def _initialize_llm(self) -> None:
        """Initialize the Bedrock LLM with proper credential handling."""
        if not BEDROCK_AVAILABLE:
            logger.warning("Bedrock not available - using mock LLM")
            self.llm = None
            return

        try:
            # Check for required environment variables
            required_vars = ["AWS_ACCESS_KEY_ID", "AWS_SECRET_ACCESS_KEY"]
            missing_vars = [var for var in required_vars if not os.environ.get(var)]

            if missing_vars:
                logger.warning(f"Missing AWS credentials: {missing_vars}. Using mock LLM.")
                self.llm = None
                return

            self.llm = ChatBedrock(
                model_id=os.environ.get("BEDROCK_MODEL_ID", "anthropic.claude-3-5-sonnet-20240620-v1:0"),
                region_name=os.environ.get("AWS_REGION", "ap-southeast-1"),
                model_kwargs={
                    "temperature": 0.1,
                    "max_tokens": 4000
                },
                streaming=True
            )
            logger.info("Initialized Bedrock LLM successfully")

        except Exception as e:
            logger.error(f"Failed to initialize Bedrock LLM: {e}")
            logger.info("Using mock LLM for testing")
            self.llm = None
    
    async def _load_available_tools(self) -> None:
        """Load available tools from MCP manager."""
        self.available_tools = self.mcp_manager.tools.copy()
        logger.info(f"Loaded {len(self.available_tools)} MCP tools")
    
    async def reload_tools(self) -> None:
        """Reload tools from MCP servers."""
        try:
            # Reinitialize MCP manager
            await self.mcp_manager.initialize()
            await self._load_available_tools()
            logger.info("Reloaded MCP tools")
        except Exception as e:
            logger.error(f"Failed to reload tools: {e}")
    
    def _format_tools_for_llm(self) -> List[Dict[str, Any]]:
        """Format available tools for LLM consumption."""
        tools = []
        
        for tool_key, tool in self.available_tools.items():
            tool_def = {
                "name": tool_key,
                "description": tool.description,
                "input_schema": tool.parameters
            }
            tools.append(tool_def)
        
        return tools
    
    async def query(self, user_message: str, conversation_history: Optional[List[Dict[str, str]]] = None) -> Dict[str, Any]:
        """Process a natural language query using Bedrock and MCP tools."""
        try:
            # Update conversation history
            if conversation_history:
                self.conversation_history = conversation_history

            # Add user message to history
            self.conversation_history.append({"role": "user", "content": user_message})

            # If LLM is not available, use direct tool matching
            if self.llm is None:
                return await self._process_without_llm(user_message)

            # Prepare tools for LLM
            available_tools = self._format_tools_for_llm()

            # Create system message
            system_message = self._create_system_message()

            # Prepare messages for LLM
            messages = [system_message]

            # Add conversation history
            for msg in self.conversation_history:
                if msg["role"] == "user":
                    messages.append(HumanMessage(content=msg["content"]))
                elif msg["role"] == "assistant":
                    messages.append(AIMessage(content=msg["content"]))

            # Process query with tool calling
            result = await self._process_with_tools(messages, available_tools)

            # Add assistant response to history
            self.conversation_history.append({"role": "assistant", "content": result.answer})

            return {
                "answer": result.answer,
                "tool_calls": [{"tool_name": tc.tool_name, "arguments": tc.arguments, "result": tc.result, "error": tc.error} for tc in result.tool_calls],
                "intermediate_steps": result.intermediate_steps,
                "conversation_history": self.conversation_history
            }

        except Exception as e:
            logger.error(f"Error processing query: {e}")
            return {
                "answer": f"I encountered an error while processing your request: {str(e)}",
                "tool_calls": [],
                "intermediate_steps": [f"Error: {str(e)}"],
                "conversation_history": self.conversation_history
            }
    
    def _create_system_message(self) -> SystemMessage:
        """Create system message for the LLM."""
        tool_descriptions = []
        for tool_key, tool in self.available_tools.items():
            tool_descriptions.append(f"- {tool_key}: {tool.description}")
        
        tools_text = "\n".join(tool_descriptions) if tool_descriptions else "No tools available"
        
        system_prompt = f"""You are an AI assistant that can help users by calling various tools when needed.

Available tools:
{tools_text}

When a user asks a question:
1. Analyze if any tools would be helpful to answer their question
2. If tools are needed, call the appropriate tools with the correct parameters
3. Use the tool results to provide a comprehensive answer
4. If no tools are needed, answer directly based on your knowledge

Always be helpful, accurate, and explain your reasoning when using tools."""
        
        return SystemMessage(content=system_prompt)

    async def _process_without_llm(self, user_message: str) -> Dict[str, Any]:
        """Process query without LLM by directly matching tools and providing information."""
        tool_calls = []
        intermediate_steps = []

        # Convert message to lowercase for matching
        message_lower = user_message.lower()

        # Check if user is asking about available tools
        if any(keyword in message_lower for keyword in ["tools", "available", "list", "what can", "help"]):
            if self.available_tools:
                tool_list = []
                for tool_key, tool in self.available_tools.items():
                    tool_list.append(f"- **{tool_key}**: {tool.description}")

                answer = f"I have access to {len(self.available_tools)} MCP tools:\n\n" + "\n".join(tool_list)
                answer += "\n\nYou can ask me to use any of these tools by describing what you want to do."
            else:
                answer = "I don't have any MCP tools available at the moment. Please check the server configuration."

        # Check if user wants to call a specific tool
        elif any(keyword in message_lower for keyword in ["superset", "dashboard", "chart", "database", "sql"]):
            # Try to find and call relevant Superset tools
            superset_tools = {k: v for k, v in self.available_tools.items() if "superset" in k.lower()}

            if superset_tools:
                # For demonstration, let's try to call a tool that lists databases or tools
                if "database" in message_lower or "list" in message_lower:
                    # Try to call a list databases tool
                    for tool_key, tool in superset_tools.items():
                        if "database" in tool.description.lower() or "list" in tool.description.lower():
                            try:
                                result = await self.mcp_manager.call_tool(tool_key, {})
                                tool_calls.append(ToolCall(
                                    tool_name=tool_key,
                                    arguments={},
                                    result=result
                                ))
                                intermediate_steps.append(f"Called {tool_key}")
                                break
                            except Exception as e:
                                tool_calls.append(ToolCall(
                                    tool_name=tool_key,
                                    arguments={},
                                    error=str(e)
                                ))
                                intermediate_steps.append(f"Failed to call {tool_key}: {e}")

                answer = f"I found {len(superset_tools)} Superset-related tools. "
                if tool_calls:
                    if tool_calls[0].result:
                        answer += f"Here's what I found:\n\n{tool_calls[0].result}"
                    elif tool_calls[0].error:
                        answer += f"I encountered an error: {tool_calls[0].error}"
                else:
                    answer += "Available Superset tools:\n" + "\n".join([f"- {k}: {v.description}" for k, v in superset_tools.items()])
            else:
                answer = "I don't have any Superset tools available. Please check if the Superset MCP server is running and configured."

        else:
            # General response
            answer = f"I understand you're asking: '{user_message}'\n\n"
            if self.available_tools:
                answer += f"I have {len(self.available_tools)} tools available. You can ask me to:\n"
                answer += "- List available tools\n"
                answer += "- Use Superset tools for dashboards and data analysis\n"
                answer += "- Get information about databases and charts\n\n"
                answer += "Try asking something like 'list available tools' or 'show me superset databases'."
            else:
                answer += "However, I don't have any tools available at the moment. Please check the MCP server configuration."

        # Add assistant response to history
        self.conversation_history.append({"role": "assistant", "content": answer})

        return {
            "answer": answer,
            "tool_calls": [{"tool_name": tc.tool_name, "arguments": tc.arguments, "result": tc.result, "error": tc.error} for tc in tool_calls],
            "intermediate_steps": intermediate_steps,
            "conversation_history": self.conversation_history
        }
    
    async def _process_with_tools(self, messages: List, available_tools: List[Dict[str, Any]]) -> QueryResult:
        """Process messages with tool calling capability."""
        tool_calls = []
        intermediate_steps = []
        
        try:
            # Call LLM with tools
            if available_tools:
                # For now, we'll use a simple approach without function calling
                # In a full implementation, you'd use the LLM's function calling capabilities
                response = await self.llm.ainvoke(messages)
                answer = response.content
            else:
                response = await self.llm.ainvoke(messages)
                answer = response.content
            
            return QueryResult(
                answer=answer,
                tool_calls=tool_calls,
                intermediate_steps=intermediate_steps,
                conversation_history=[]
            )
            
        except Exception as e:
            logger.error(f"Error in tool processing: {e}")
            return QueryResult(
                answer=f"I encountered an error: {str(e)}",
                tool_calls=tool_calls,
                intermediate_steps=intermediate_steps + [f"Error: {str(e)}"],
                conversation_history=[]
            )
    
    async def stream_query(self, user_message: str) -> AsyncIterator[Dict[str, Any]]:
        """Stream the query processing with real-time updates."""
        try:
            yield {"type": "status", "message": "Processing query..."}
            
            # Process the query
            result = await self.query(user_message)
            
            # Stream the response
            yield {"type": "tool_calls", "data": result["tool_calls"]}
            yield {"type": "intermediate_steps", "data": result["intermediate_steps"]}
            yield {"type": "answer", "data": result["answer"]}
            yield {"type": "complete", "data": result}
            
        except Exception as e:
            logger.error(f"Error in streaming query: {e}")
            yield {"type": "error", "message": str(e)}
