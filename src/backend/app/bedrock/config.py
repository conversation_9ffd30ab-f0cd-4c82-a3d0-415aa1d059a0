"""
Bedrock MCP Configuration Manager

Manages MCP server configurations specifically for Bedrock integration.
Extends the base MCP configuration with Bedrock-specific features.
"""

import json
import logging
import os
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict

# Import base MCP config components
import sys
# Add the project root to the path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
sys.path.insert(0, project_root)

try:
    from app.ai_gateway.mcp_config import MCPConfigManager, MCPServerConfig
except ImportError:
    # Fallback for development
    sys.path.insert(0, os.path.join(project_root, 'app'))
    from ai_gateway.mcp_config import MCPConfigManager, MCPServerConfig

logger = logging.getLogger(__name__)


@dataclass
class BedrockMCPServer:
    """Bedrock-specific MCP server configuration."""
    name: str
    type: str
    url: Optional[str] = None
    description: Optional[str] = None
    enabled: bool = True
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return asdict(self)


class BedrockMCPConfigManager:
    """Manages MCP server configurations for Bedrock integration."""
    
    def __init__(self, config_path: Optional[str] = None):
        # Use backend-specific config path
        self.config_path = config_path or "src/backend/.stitch/mcp.json"
        self.servers: Dict[str, BedrockMCPServer] = {}
        self.base_manager = MCPConfigManager(self.config_path)
        
    def load_config(self) -> bool:
        """Load MCP server configurations from JSON file."""
        try:
            config_file = Path(self.config_path)
            if not config_file.exists():
                logger.warning(f"Bedrock MCP config file not found: {self.config_path}")
                # Create default config
                self._create_default_config()
                return True
                
            with open(config_file, 'r') as f:
                config_data = json.load(f)
                
            # Parse server configurations
            mcp_servers = config_data.get('mcpServers', {})
            
            for server_name, server_config in mcp_servers.items():
                if server_config.get('disabled', False):
                    logger.info(f"Skipping disabled MCP server: {server_name}")
                    continue
                    
                # Create Bedrock server configuration
                server = BedrockMCPServer(
                    name=server_name,
                    type=server_config.get('type', 'http'),
                    url=server_config.get('url'),
                    description=server_config.get('description', ''),
                    enabled=not server_config.get('disabled', False)
                )
                
                self.servers[server_name] = server
                logger.info(f"Loaded Bedrock MCP server config: {server_name} ({server.type})")
                
            logger.info(f"Loaded {len(self.servers)} Bedrock MCP server configurations")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load Bedrock MCP config from {self.config_path}: {e}")
            return False
    
    def _create_default_config(self) -> None:
        """Create a default configuration file."""
        try:
            config_dir = Path(self.config_path).parent
            config_dir.mkdir(parents=True, exist_ok=True)
            
            default_config = {
                "mcpServers": {
                    "superset": {
                        "url": "http://localhost:8001/mcp",
                        "type": "streamable-http",
                        "disabled": False,
                        "description": "Apache Superset MCP server for dashboards, charts, and SQL queries",
                        "alwaysAllow": []
                    }
                }
            }
            
            with open(self.config_path, 'w') as f:
                json.dump(default_config, f, indent=2)
                
            logger.info(f"Created default Bedrock MCP config at {self.config_path}")
            
        except Exception as e:
            logger.error(f"Failed to create default config: {e}")
    
    def get_server(self, name: str) -> Optional[BedrockMCPServer]:
        """Get configuration for a specific server."""
        return self.servers.get(name)
    
    def get_all_servers(self) -> Dict[str, BedrockMCPServer]:
        """Get all server configurations."""
        return self.servers.copy()
    
    def add_server(self, name: str, url: str, server_type: str = "streamable-http", 
                   description: str = "") -> bool:
        """Add a new MCP server configuration."""
        if name in self.servers:
            logger.warning(f"Server {name} already exists")
            return False
            
        server = BedrockMCPServer(
            name=name,
            type=server_type,
            url=url,
            description=description,
            enabled=True
        )
        
        self.servers[name] = server
        logger.info(f"Added Bedrock MCP server: {name}")
        return True
    
    def remove_server(self, name: str) -> bool:
        """Remove an MCP server configuration."""
        if name not in self.servers:
            logger.warning(f"Server {name} not found")
            return False
            
        del self.servers[name]
        logger.info(f"Removed Bedrock MCP server: {name}")
        return True
    
    def save_config(self) -> bool:
        """Save current configurations to JSON file."""
        try:
            config_data = {
                "mcpServers": {}
            }
            
            for server_name, server in self.servers.items():
                server_config = {
                    "type": server.type,
                    "disabled": not server.enabled
                }
                
                if server.url:
                    server_config["url"] = server.url
                if server.description:
                    server_config["description"] = server.description
                    
                config_data["mcpServers"][server_name] = server_config
            
            # Ensure directory exists
            config_dir = Path(self.config_path).parent
            config_dir.mkdir(parents=True, exist_ok=True)
            
            with open(self.config_path, 'w') as f:
                json.dump(config_data, f, indent=2)
                
            logger.info(f"Saved Bedrock MCP config to {self.config_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save Bedrock MCP config: {e}")
            return False
    
    def validate_server_config(self, name: str, url: str, server_type: str) -> List[str]:
        """Validate server configuration parameters."""
        errors = []
        
        if not name or not name.strip():
            errors.append("Server name is required")
        
        if not url or not url.strip():
            errors.append("Server URL is required")
        
        if server_type not in ["http", "streamable-http", "subprocess"]:
            errors.append("Server type must be 'http', 'streamable-http', or 'subprocess'")
        
        if name in self.servers:
            errors.append(f"Server with name '{name}' already exists")
        
        return errors
    
    def get_config_summary(self) -> Dict[str, Any]:
        """Get a summary of the current configuration."""
        enabled_servers = [s for s in self.servers.values() if s.enabled]
        
        return {
            "total_servers": len(self.servers),
            "enabled_servers": len(enabled_servers),
            "server_types": list(set(s.type for s in self.servers.values())),
            "config_path": self.config_path
        }
